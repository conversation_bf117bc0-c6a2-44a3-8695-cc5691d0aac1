# Astro

```sh
npm create astro@latest -- --template basics
```

Borrar `node_modules/`, mover `typescript` a `devDependencies` y `npm i`.

Crear `./public/robots.txt` y `tsconfig.json`.

Configurar el dominio en `astro.config.mjs`, en mi caso `site: "https://bookea.link"`

Agregar que chequee TypeScript antes del build. Instalar `npm i @astrojs/check -D` y en `package.json`:

```json
"scripts": {
  "build": "astro check && astro build",
}
```

Agregar transiciones: https://docs.astro.build/en/guides/view-transitions/#full-site-view-transitions-spa-mode

## Google Analytics

Utilicé https://github.com/handystudio/astro-google-analytics#readme

Seguir el readme y como se dice allí, crear `.env` con `GA_ID=G-xxxxxx` que dice en:

<p align="center">
    <img src="docs/google%20analytics.png" />
</p>

Link de GA: https://analytics.google.com/analytics/web/#/p491398868

Para que no se contamine con mi development, setié ese valor en la env var de Netlify pero borré el archivo `.env` de este directorio.

Link al realtime overview:<br />
https://analytics.google.com/analytics/web/#/p491398868/realtime/overview?params=_u..nav%3Dmaui

## Traducciones

Probé https://github.com/yassinedoghri/astro-i18next pero no funca al deployar en Netlify

## Agregar Tailwind

https://docs.astro.build/en/guides/styling/#add-tailwind-4

## Creación del logo

Probé Google (debí usar https://aistudio.google.com y seleccionar `gemini-2.0-flash-preview-image-generation`, xq el chat de [Gemini](https://gemini.google.com/) no usaba un modelo específico de texto-a-imagen) (`imagen-3.0-generate-002`), ChatGPT (DALL·E 3) y Recraft V3 Raw. ChatGPT fue el mejor: https://chatgpt.com/c/683fd186-b690-8007-b471-6f23f4f18ab9

Ver carpeta `src/assests/logo/`

Si embargo agoté los prompts gratuitos diarios, probar estos prompts:
Que la cadena que representa a "link" tenga múltiple eslabones
Que la cadena dibuje el check dentro del calendario.
Crear una versión simplificada para usar como favicon.
Generar versión con el texto a la derecha, en lugar de abajo.

Para convertir de png a svg: https://convertio.co/es/png-svg/ hay que agregarle el color agregando `fill="#000000"` con el color deseado a los paths.
Recortar el svg: https://svgcrop.com/

