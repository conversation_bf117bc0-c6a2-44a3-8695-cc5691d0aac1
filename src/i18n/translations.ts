export const translations = {
  en: {

    pages: {
      index: {
        go_to_holis: 'Go to holis',
      },
      holis: {
        title: 'Holis English',
        go_to_home: 'Go to home',
      },
    },

    Layout: {
      title: 'bookea.link',
      description: 'Booking for all types of businesses',
    },

    components: {
      Welcome: {
        get_started: 'To get started, open the',
        directory: 'directory in your project.',
        read_docs: 'Read our docs',
        join_discord: 'Join our Discord',
        whats_new_title: "What's New in Astro 5.0?",
        whats_new_description: 'From content layers to server islands, click to learn more about the new features and improvements in Astro 5.0',
      },
    },
  },

  es: {
    Layout: {
      title: 'bookea.link',
      description: 'Reservas para todo tipo de negocios',
    },

    pages: {
      index: {
        go_to_holis: 'Ir a holis',
      },
      holis: {
        title: 'Holis español',
        go_to_home: 'Ir a inicio',
      },
    },

    components: {
      Welcome: {
        get_started: 'Para comenzar, abre el',
        directory: 'directorio en tu proyecto.',
        read_docs: 'Lee nuestra documentación',
        join_discord: 'Únete a nuestro Discord',
        whats_new_title: '¿Qué hay de nuevo en Astro 5.0?',
        whats_new_description: 'Desde capas de contenido hasta islas de servidor, haz clic para conocer más sobre las nuevas características y mejoras en Astro 5.0',
      },
    },
  }
} as const;

export type TranslationKey = keyof typeof translations.en;
