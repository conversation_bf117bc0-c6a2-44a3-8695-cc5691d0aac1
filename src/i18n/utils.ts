import { translations } from './translations';

export type Locale = 'en' | 'es';

export const defaultLocale: Locale = 'en';
export const locales: Locale[] = ['en', 'es'];

export function getLocaleFromUrl(url: URL): Locale {
  const [, locale] = url.pathname.split('/');
  if (locale && locales.includes(locale as Locale)) {
    return locale as Locale;
  }
  return defaultLocale;
}

export function useTranslations(locale: Locale) {
  return function t(key: string): string {
    // Handle dot notation for nested objects
    const keys = key.split('.');
    let result = translations[locale];

    // Navigate through nested objects
    for (const k of keys) {
      if (result && typeof result === 'object' && k in result) {
        // @ts-expect-error
        result = result[k];
      } else {
        // Fall back to default locale
        // @ts-expect-error
        result = getNestedValue(translations[defaultLocale], keys);
        if (result === undefined) return key;
        break;
      }
    }

    return typeof result === 'string' ? result : key;
  };
}

// Helper function to get nested value
function getNestedValue(obj: any, keys: string[]): string | undefined {
  let result = obj;
  for (const k of keys) {
    if (result && typeof result === 'object' && k in result) {
      result = result[k];
    } else {
      return undefined;
    }
  }
  return typeof result === 'string' ? result : undefined;
}

export function getLocalizedPath(path: string, locale: Locale): string {
  if (locale === defaultLocale) {
    return path;
  }
  return `/${locale}${path}`;
}

export function removeLocaleFromPath(path: string): string {
  const segments = path.split('/');
  if (segments[1] && locales.includes(segments[1] as Locale)) {
    return '/' + segments.slice(2).join('/');
  }
  return path;
}

export function getAlternateLocale(currentLocale: Locale): Locale {
  return currentLocale === 'en' ? 'es' : 'en';
}

export function getLocaleDisplayName(locale: Locale): string {
  const names = {
    en: 'English',
    es: 'Español'
  };
  return names[locale];
}

// Get translation
export function getT(kTrans: string, astro: unknown) {
  return (key: string) => useTranslations(getCL(astro))(kTrans + key)
}

// Get current locale
export function getCL(astro: any) {
  const { lang } = astro.params;
  return (lang as Locale) || 'en';
}

// Get path
export function getP(astro: unknown) {
  return (path: string) => getLocalizedPath(path, getCL(astro));
}

// Get language utils
export function getL(kTrans: string, astro: unknown) {
  return {
    t: getT(kTrans, astro),
    p: getP(astro),
  };
}
