---
import "../styles/global.css";
import GoogleAnalytics from '@astro-kits/google-analytics';
import { ClientRouter } from "astro:transitions";
import LanguageSwitcher from '../components/LanguageSwitcher.astro';
import { getCL, getL } from '../i18n/utils';

const { t } = getL('Layout.', Astro);
const currentLocale = getCL(Astro);
---
<!doctype html>
<html lang={currentLocale}>
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />
		<title>{t('title')}</title>
    <meta name="description" content={t('description')} />
    <ClientRouter />
    <GoogleAnalytics />
	</head>
	<body>
		<LanguageSwitcher />
		<slot />
	</body>
</html>

<style>
	html,
	body {
		margin: 0;
		width: 100%;
		height: 100%;
	}
</style>
