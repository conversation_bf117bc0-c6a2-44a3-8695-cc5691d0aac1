---
import Layout from '../../layouts/Layout.astro';
import { getL } from "../../i18n/utils";

const { t, p } = getL('pages.holis.', Astro);

export async function getStaticPaths() {
  return [
    { params: { lang: undefined } }, // English (no prefix) - generates /holis
    { params: { lang: 'es' } }       // Spanish - generates /es/holis
  ];
}
---

<Layout>
  <div style="padding: 2rem; text-align: center;">
    <h1>{t('title')}</h1>
    <a href={p('/')} style="display: inline-block; margin-top: 1rem; padding: 8px 16px; background: #3245ff; color: white; text-decoration: none; border-radius: 6px;">
      {t('go_to_home')}
    </a>
  </div>
</Layout>
