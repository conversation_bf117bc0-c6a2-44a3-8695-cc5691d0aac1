---
import Welcome from '../../components/Welcome.astro';
import Layout from '../../layouts/Layout.astro';
import { getL } from '../../i18n/utils';

const { t, p } = getL('pages.index.', Astro);

export async function getStaticPaths() {
  return [
    { params: { lang: undefined } }, // English (no prefix) - generates /
    { params: { lang: 'es' } }       // Spanish - generates /es/
  ];
}

---

<Layout>
  <a href={p('/holis')} style="position: fixed; top: 80px; right: 20px; z-index: 1000; padding: 8px 16px; background: rgba(255, 255, 255, 0.9); border: 1px solid #ddd; border-radius: 6px; text-decoration: none; color: #333;">
    {t('go_to_holis')}
  </a>
	<Welcome />
</Layout>
